#!/usr/bin/env node

/**
 * 清理和重置合同审核系统数据库脚本
 * 删除所有测试数据并重新生成简化的用户结构
 */

const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcrypt');
const path = require('path');
const fs = require('fs');
const DATABASE_CONFIG = require('./src/config/database');

// 使用统一的数据库配置
const DB_PATH = DATABASE_CONFIG.path;
const UPLOAD_DIR = path.join(__dirname, 'uploads');
const AVATARS_DIR = path.join(UPLOAD_DIR, 'avatars');

console.log('🧹 开始清理和重置合同审核系统...');

// 创建数据库连接
const db = new sqlite3.Database(DB_PATH, (err) => {
  if (err) {
    console.error('❌ 数据库连接失败:', err.message);
    process.exit(1);
  }
  console.log('✅ 数据库连接成功:', DB_PATH);
});

// Promise化数据库操作
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (err) {
      if (err) {
        reject(err);
      } else {
        resolve({ changes: this.changes, lastID: this.lastID });
      }
    });
  });
}

// 清理文件夹中的所有文件
function cleanDirectory(dirPath, description) {
  if (!fs.existsSync(dirPath)) {
    console.log(`📁 目录不存在，跳过清理: ${dirPath}`);
    return;
  }

  const files = fs.readdirSync(dirPath);
  let deletedCount = 0;

  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);

    if (stat.isFile()) {
      try {
        fs.unlinkSync(filePath);
        deletedCount++;
      } catch (error) {
        console.error(`❌ 删除文件失败: ${filePath}`, error.message);
      }
    }
  });

  console.log(`🗑️ 清理${description}: 删除了 ${deletedCount} 个文件`);
}

// 主清理和重置函数
async function cleanAndReset() {
  try {
    console.log('\n📋 第一步：清理上传文件...');

    // 清理PDF文件
    cleanDirectory(UPLOAD_DIR, 'PDF文件');

    // 清理头像文件
    cleanDirectory(AVATARS_DIR, '头像文件');

    console.log('\n📋 第二步：清理数据库数据...');

    // 启用外键约束
    await runQuery('PRAGMA foreign_keys = ON');

    // 清理合同相关数据
    const contractsResult = await runQuery('DELETE FROM contracts');
    console.log(`🗑️ 清理合同数据: 删除了 ${contractsResult.changes} 条记录`);

    // 清理通知数据
    const notificationsResult = await runQuery('DELETE FROM notifications');
    console.log(`🗑️ 清理通知数据: 删除了 ${notificationsResult.changes} 条记录`);

    // 清理操作日志
    const logsResult = await runQuery('DELETE FROM operation_logs');
    console.log(`🗑️ 清理操作日志: 删除了 ${logsResult.changes} 条记录`);

    // 清理所有用户数据
    const usersResult = await runQuery('DELETE FROM users');
    console.log(`🗑️ 清理用户数据: 删除了 ${usersResult.changes} 条记录`);

    // 重置自增ID
    await runQuery('DELETE FROM sqlite_sequence WHERE name IN ("users", "contracts", "notifications", "operation_logs")');
    console.log('🔄 重置自增ID计数器');

    console.log('\n📋 第三步：创建简化的用户数据...');

    // 生成加密密码
    const saltRounds = process.env.NODE_ENV === 'production' ? 6 : 4;
    const adminPassword = await bcrypt.hash('admin123', saltRounds);
    const defaultPassword = await bcrypt.hash('123456', saltRounds);

    // 创建简化的用户结构
    const users = [
      { username: 'admin', password: adminPassword, role: 'admin', role_id: 4, description: '系统管理员' },
      { username: 'employee', password: defaultPassword, role: 'employee', role_id: 1, description: '员工' },
      { username: 'county_reviewer', password: defaultPassword, role: 'county_reviewer', role_id: 2, description: '县级审核员' },
      { username: 'city_reviewer', password: defaultPassword, role: 'city_reviewer', role_id: 3, description: '市级审核员' }
    ];

    for (const user of users) {
      const result = await runQuery(`
        INSERT INTO users (username, password, role, role_id, status, created_by)
        VALUES (?, ?, ?, ?, 'active', ?)
      `, [user.username, user.password, user.role, user.role_id, user.username === 'admin' ? null : 1]);

      console.log(`✅ 创建用户: ${user.username} (${user.description}) - ID: ${result.lastID}`);
    }

    console.log('\n🎉 清理和重置完成！');
    console.log('\n📝 新的用户账号信息：');
    console.log('  管理员: admin / admin123');
    console.log('  员工: employee / 123456');
    console.log('  县级审核员: county_reviewer / 123456');
    console.log('  市级审核员: city_reviewer / 123456');

  } catch (error) {
    console.error('❌ 清理和重置失败:', error.message);
    process.exit(1);
  } finally {
    db.close((err) => {
      if (err) {
        console.error('❌ 关闭数据库连接失败:', err.message);
      } else {
        console.log('✅ 数据库连接已关闭');
      }
    });
  }
}

// 执行清理和重置
cleanAndReset();
