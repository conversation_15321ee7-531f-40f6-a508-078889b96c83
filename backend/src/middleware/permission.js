/**
 * 权限管理中间件
 * 基于角色的访问控制 (RBAC)
 */

const { USER_ROLES, PERMISSIONS, ROLE_PERMISSIONS, HTTP_STATUS, RESPONSE_MESSAGES } = require('../utils/constants');
const { ResponseUtils } = require('../utils/helpers');

/**
 * 检查用户是否具有指定角色
 * @param {string|Array} roles - 允许的角色
 * @returns {Function} 中间件函数
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
        );
      }

      const userRole = req.user.role;
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      if (!allowedRoles.includes(userRole)) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error(RESPONSE_MESSAGES.FORBIDDEN, HTTP_STATUS.FORBIDDEN)
        );
      }

      next();
    } catch (error) {
      console.error('角色检查错误:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  };
};

/**
 * 检查用户是否具有指定权限
 * @param {string|Array} permissions - 需要的权限
 * @returns {Function} 中间件函数
 */
const requirePermission = (permissions) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
        );
      }

      const userRole = req.user.role;
      const userPermissions = ROLE_PERMISSIONS[userRole] || [];
      const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];

      // 检查用户是否具有所有必需的权限
      const hasAllPermissions = requiredPermissions.every(permission =>
        userPermissions.includes(permission)
      );

      if (!hasAllPermissions) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error(RESPONSE_MESSAGES.FORBIDDEN, HTTP_STATUS.FORBIDDEN)
        );
      }

      next();
    } catch (error) {
      console.error('权限检查错误:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  };
};

/**
 * 检查用户是否具有任一指定权限
 * @param {Array} permissions - 权限列表
 * @returns {Function} 中间件函数
 */
const requireAnyPermission = (permissions) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
        );
      }

      const userRole = req.user.role;
      const userPermissions = ROLE_PERMISSIONS[userRole] || [];

      // 检查用户是否具有任一权限
      const hasAnyPermission = permissions.some(permission =>
        userPermissions.includes(permission)
      );

      if (!hasAnyPermission) {
        return res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseUtils.error(RESPONSE_MESSAGES.FORBIDDEN, HTTP_STATUS.FORBIDDEN)
        );
      }

      next();
    } catch (error) {
      console.error('权限检查错误:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  };
};

/**
 * 管理员权限中间件
 */
const requireAdmin = requireRole(USER_ROLES.ADMIN);

/**
 * 审核员权限中间件 (包括管理员)
 */
const requireReviewer = requireRole([USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER, USER_ROLES.ADMIN]);

/**
 * 员工权限中间件 (包括所有角色)
 */
const requireEmployee = requireRole([USER_ROLES.EMPLOYEE, USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER, USER_ROLES.ADMIN]);

/**
 * 资源所有者权限检查
 * 检查用户是否为资源的所有者
 * @param {string} resourceIdParam - 资源ID参数名
 * @param {string} ownerField - 所有者字段名
 * @returns {Function} 中间件函数
 */
const requireOwnership = (resourceIdParam = 'id', ownerField = 'submitter_id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
        );
      }

      // 管理员可以访问所有资源
      if (req.user.role === USER_ROLES.ADMIN) {
        return next();
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        return res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseUtils.error('缺少资源ID', HTTP_STATUS.BAD_REQUEST)
        );
      }

      // 将资源ID和所有者字段添加到请求对象，供后续中间件使用
      req.resourceOwnership = {
        resourceId,
        ownerField,
        userId: req.user.id
      };

      next();
    } catch (error) {
      console.error('所有权检查错误:', error);
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  };
};

/**
 * 合同修改权限检查
 * 检查用户是否可以修改指定合同
 */
const canModifyContract = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 管理员可以查看但不能修改合同内容
    if (req.user.role === USER_ROLES.ADMIN) {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('管理员不能修改合同内容', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 合同修改权限检查将在合同路由中进一步验证
    next();
  } catch (error) {
    console.error('合同修改权限检查错误:', error);
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 合同审核权限检查
 * 检查用户是否可以审核指定合同
 */
const canReviewContract = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseUtils.error(RESPONSE_MESSAGES.UNAUTHORIZED, HTTP_STATUS.UNAUTHORIZED)
      );
    }

    // 只有审核员和管理员可以审核合同
    if (![USER_ROLES.REVIEWER, USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER, USER_ROLES.ADMIN].includes(req.user.role)) {
      return res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseUtils.error('只有审核员可以审核合同', HTTP_STATUS.FORBIDDEN)
      );
    }

    // 合同审核权限检查将在合同路由中进一步验证
    next();
  } catch (error) {
    console.error('合同审核权限检查错误:', error);
    return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
    );
  }
};

/**
 * 获取用户权限列表
 * @param {string} role - 用户角色
 * @returns {Array} 权限列表
 */
const getUserPermissions = (role) => {
  return ROLE_PERMISSIONS[role] || [];
};

/**
 * 检查用户是否具有指定权限
 * @param {string} role - 用户角色
 * @param {string} permission - 权限
 * @returns {boolean} 是否具有权限
 */
const hasPermission = (role, permission) => {
  const userPermissions = getUserPermissions(role);
  return userPermissions.includes(permission);
};

/**
 * 权限信息中间件
 * 将用户权限信息添加到请求对象
 */
const addPermissionInfo = (req, res, next) => {
  if (req.user) {
    req.userPermissions = getUserPermissions(req.user.role);
    req.hasPermission = (permission) => hasPermission(req.user.role, permission);
  }
  next();
};

module.exports = {
  requireRole,
  requirePermission,
  requireAnyPermission,
  requireAdmin,
  requireReviewer,
  requireEmployee,
  requireOwnership,
  canModifyContract,
  canReviewContract,
  getUserPermissions,
  hasPermission,
  addPermissionInfo
};
