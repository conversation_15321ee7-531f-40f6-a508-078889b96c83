<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <el-header class="layout-header">
      <div class="header-left">
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon :size="20">
            <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
          </el-icon>
        </div>
        <h1 class="logo">合同审核系统</h1>
      </div>

      <div class="header-right">
        <div class="header-actions">
          <!-- 通知 -->
          <el-popover
            placement="bottom-end"
            :width="320"
            trigger="click"
            popper-class="notification-popover"
          >
            <template #reference>
              <el-badge
                :value="notificationStore.unreadCount"
                :hidden="notificationStore.unreadCount === 0"
                class="notification-badge"
              >
                <el-button text>
                  <el-icon :size="18"><Bell /></el-icon>
                </el-button>
              </el-badge>
            </template>

            <!-- 通知下拉内容 -->
            <div class="notification-dropdown">
              <div class="notification-header">
                <h4>通知消息</h4>
                <el-button
                  v-if="notificationStore.unreadCount > 0"
                  text
                  size="small"
                  @click="markAllNotificationsRead"
                >
                  全部已读
                </el-button>
              </div>

              <div
                v-loading="notificationStore.loading"
                class="notification-list"
              >
                <div
                  v-if="notificationStore.notifications.length === 0"
                  class="notification-empty"
                >
                  <el-icon :size="48" color="#c0c4cc"><Bell /></el-icon>
                  <p>暂无通知</p>
                </div>

                <div
                  v-for="notification in displayNotifications"
                  :key="notification.id"
                  class="notification-item"
                  :class="{
                    'notification-item--unread': !notification.is_read,
                  }"
                  @click="handleNotificationClick(notification)"
                >
                  <div class="notification-content">
                    <div class="notification-title">
                      {{ notification.title }}
                      <span
                        v-if="!notification.is_read"
                        class="unread-dot"
                      ></span>
                    </div>
                    <div class="notification-text">
                      {{ notification.content }}
                    </div>
                    <div class="notification-time">
                      {{ formatNotificationTime(notification.created_at) }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="notification-footer">
                <el-button text @click="viewAllNotifications"
                  >查看全部通知</el-button
                >
              </div>
            </div>
          </el-popover>

          <!-- 用户信息 -->
          <el-dropdown class="user-dropdown" @command="handleUserCommand">
            <div class="user-info">
              <div class="user-avatar">
                <img
                  v-if="userAvatar"
                  :src="userAvatar"
                  :alt="userName"
                  class="avatar-image"
                />
                <span v-else class="avatar-initial">{{ userInitial }}</span>
              </div>
              <div class="user-details">
                <span class="user-name">{{ userName }}</span>
              </div>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人设置
                </el-dropdown-item>
                <el-dropdown-item command="changePassword">
                  <el-icon><Lock /></el-icon>
                  修改密码
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <!-- 主体内容 -->
    <el-container class="layout-body">
      <!-- 左侧边栏 -->
      <el-aside
        :width="sidebarWidth"
        class="layout-sidebar"
        :class="{ 'is-collapsed': sidebarCollapsed }"
      >
        <TheSidebar
          ref="sidebarRef"
          :collapsed="sidebarCollapsed"
          @menu-select="handleMenuSelect"
        />
      </el-aside>

      <!-- 右侧内容区 -->
      <el-main class="layout-main">
        <!-- Tab 管理区域 -->
        <div class="tab-container">
          <TabManager
            :tabs="tabs"
            :active-tab="activeTab"
            @tab-change="handleTabChange"
            @tab-close="handleTabClose"
            @update:active-tab="handleActiveTabUpdate"
          />
        </div>

        <!-- 页面内容区域 -->
        <div class="page-content">
          <!-- 使用Tab系统管理页面显示，完全避免路由跳转和页面刷新 -->
          <keep-alive>
            <component
              :is="currentComponent"
              v-if="currentComponent"
              :key="`tab-${activeTab}`"
              v-bind="currentTabParams"
            />
          </keep-alive>

          <!-- 默认内容 -->
          <div v-if="!$route.matched.length" class="content-placeholder">
            <el-icon :size="64" class="placeholder-icon">
              <Document />
            </el-icon>
            <h2 class="placeholder-title">欢迎使用合同审核系统</h2>
            <p class="placeholder-description">
              请从左侧菜单选择功能模块开始使用
            </p>
            <div class="feature-list">
              <div class="feature-item completed">
                <el-icon><Check /></el-icon>
                <span>项目架构设计</span>
              </div>
              <div class="feature-item completed">
                <el-icon><Check /></el-icon>
                <span>数据库设计</span>
              </div>
              <div class="feature-item completed">
                <el-icon><Check /></el-icon>
                <span>后端基础架构</span>
              </div>
              <div class="feature-item completed">
                <el-icon><Check /></el-icon>
                <span>前端基础架构</span>
              </div>
              <div class="feature-item completed">
                <el-icon><Check /></el-icon>
                <span>用户认证系统</span>
              </div>
              <div class="feature-item completed">
                <el-icon><Check /></el-icon>
                <span>权限管理系统</span>
              </div>
              <div class="feature-item completed">
                <el-icon><Check /></el-icon>
                <span>主布局组件</span>
              </div>
              <div class="feature-item pending">
                <el-icon><Clock /></el-icon>
                <span>Tab 管理系统</span>
              </div>
              <div class="feature-item pending">
                <el-icon><Clock /></el-icon>
                <span>侧边栏菜单系统</span>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>

    <!-- 修改密码对话框 -->
    <ChangePasswordDialog
      v-model="showChangePasswordDialog"
      @success="handlePasswordChanged"
    />
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  markRaw,
  watch,
  nextTick,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Bell,
  User,
  Lock,
  SwitchButton,
  ArrowDown,
  Document,
  Check,
  Clock,
} from "@element-plus/icons-vue";

import { useTabs } from "@/composables/useTabs";

import TabManager from "@/components/layout/TabManager.vue";
import TheSidebar from "@/components/layout/TheSidebar.vue";
import ChangePasswordDialog from "@/components/common/ChangePasswordDialog.vue";

// 导入页面组件
import HomePage from "@/views/HomePage.vue";
import UserManagePage from "@/views/UserManagePage.vue";
import ContractManagePage from "@/views/ContractManagePage.vue";
import SystemManagePage from "@/views/SystemManagePage.vue";
import ProfilePage from "@/views/ProfilePage.vue";
import SettingsPage from "@/views/SettingsPage.vue";
import SubmitPage from "@/views/SubmitPage.vue";
import MyContractsPage from "@/views/MyContractsPage.vue";
import ReviewManagePage from "@/views/ReviewManagePage.vue";
import StatisticsPage from "@/views/StatisticsPage.vue";
import ContractDetailPage from "@/views/ContractDetailPage.vue";
import ContractDetailTab from "@/components/contract/ContractDetailTab.vue";
import NotificationsPage from "@/views/NotificationsPage.vue";

const router = useRouter();
const route = useRoute();

// 认证和权限
import { useUserStore } from "@/stores/user";
import { useNotificationStore } from "@/stores/notifications";
import { getAvatarUrl } from "@/utils/config";
const userStore = useUserStore();
const notificationStore = useNotificationStore();

// Tab 管理
const {
  tabs,
  activeTab,
  openTab,
  closeTab,
  setActiveTab,
  initDefaultTab,
  clearTabsStorage,
} = useTabs();

// 侧边栏引用
const sidebarRef = ref();

// 组件映射
const componentMap = {
  home: HomePage,
  "user-management": UserManagePage,
  "contract-management": ContractManagePage,
  "system-settings": SettingsPage,
  "system-stats": SystemManagePage,
  settings: SettingsPage,
  profile: ProfilePage,
  submit: SubmitPage,
  "my-contracts": MyContractsPage,
  // 审核员统一的审核管理页面
  "review-management": ReviewManagePage,
  statistics: StatisticsPage,
  // 合同详情页面
  "contract-detail": ContractDetailPage,
  // 合同详情Tab组件
  ContractDetailTab: ContractDetailTab,
  // 通知中心页面
  notifications: NotificationsPage,
  NotificationsPage: NotificationsPage,
};

// 当前显示的组件
const currentComponent = computed(() => {
  const currentTab = tabs.value.find((tab) => tab.name === activeTab.value);
  if (currentTab && currentTab.component) {
    const component = componentMap[currentTab.component];
    if (component) {
      return markRaw(component);
    }
  }

  // 回退到直接映射
  const component = componentMap[activeTab.value] || HomePage;
  return component;
});

// 当前Tab的参数
const currentTabParams = computed(() => {
  const currentTab = tabs.value.find((tab) => tab.name === activeTab.value);
  return currentTab?.params || {};
});

// 侧边栏状态 - 默认展开
const sidebarCollapsed = ref(false);
const sidebarWidth = computed(() =>
  sidebarCollapsed.value ? "64px" : "180px",
);

// 通知相关功能将通过notificationStore管理

// 对话框状态
const showChangePasswordDialog = ref(false);

// 用户信息
const userName = computed(() => {
  if (!userStore.user) return "未登录";
  return userStore.user.username || "用户";
});

const userInitial = computed(() => {
  const name = userName.value;
  return name && name !== "未登录" ? name.charAt(0).toUpperCase() : "U";
});

const userAvatar = computed(() => {
  // 从用户store获取头像URL
  if (userStore.user?.avatar) {
    return getAvatarUrl(userStore.user.avatar);
  }
  return "";
});

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
  // 保存状态到本地存储
  localStorage.setItem("sidebarCollapsed", sidebarCollapsed.value.toString());
};

// 处理菜单选择
const handleMenuSelect = (menuItem) => {
  // 阻止任何可能的默认行为
  event?.preventDefault?.();
  event?.stopPropagation?.();

  // 打开Tab页面，这里不会触发路由跳转，只是切换组件显示
  openTab(menuItem);

  // 确保侧边栏菜单状态与Tab状态保持一致
  if (sidebarRef.value && sidebarRef.value.setActiveMenu) {
    sidebarRef.value.setActiveMenu(menuItem.key);
  }
};

// 处理 Tab 变化
const handleTabChange = () => {
  // Tab 变化逻辑在 useTabs 中处理
};

// 处理 Tab 关闭
const handleTabClose = (tabName) => {
  closeTab(tabName);
};

// 处理 activeTab 更新
const handleActiveTabUpdate = (tabName) => {
  setActiveTab(tabName);

  // 同步更新侧边栏菜单激活状态，避免菜单状态刷新
  if (sidebarRef.value && sidebarRef.value.setActiveMenu) {
    sidebarRef.value.setActiveMenu(tabName);
  }
};

// 通知相关功能
const displayNotifications = computed(() => {
  // 显示最近的10条通知
  return notificationStore.notifications.slice(0, 10);
});

// 格式化通知时间
const formatNotificationTime = (timeString) => {
  const time = new Date(timeString);
  const now = new Date();
  const diff = now - time;

  // 小于1分钟
  if (diff < 60000) {
    return "刚刚";
  }
  // 小于1小时
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`;
  }
  // 小于1天
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`;
  }
  // 小于7天
  if (diff < 604800000) {
    return `${Math.floor(diff / 86400000)}天前`;
  }
  // 超过7天显示具体日期
  return time.toLocaleDateString();
};

// 处理通知点击
const handleNotificationClick = async (notification) => {
  try {
    // 如果是未读通知，标记为已读
    if (!notification.is_read) {
      await notificationStore.markAsRead(notification.id);
    }

    // 根据通知类型进行相应的跳转
    if (notification.related_type === "contract" && notification.related_id) {
      // 跳转到合同详情
      openTab({
        key: `contract-detail-${notification.related_id}`,
        title: `合同详情`,
        path: `/contracts/${notification.related_id}`,
        component: "ContractDetailTab",
        icon: "Document",
        params: { contractId: notification.related_id },
      });
    }

    ElMessage.success("通知已查看");
  } catch (error) {
    console.error("处理通知点击失败:", error);
    ElMessage.error("操作失败");
  }
};

// 标记所有通知为已读
const markAllNotificationsRead = async () => {
  try {
    await notificationStore.markAllAsRead();
    ElMessage.success("所有通知已标记为已读");
  } catch (error) {
    console.error("标记全部已读失败:", error);
    ElMessage.error("操作失败");
  }
};

// 查看全部通知
const viewAllNotifications = () => {
  // 打开通知管理页面
  openTab({
    key: "notifications",
    title: "通知中心",
    path: "/notifications",
    component: "NotificationsPage",
    icon: "Bell",
  });
};

// 处理用户下拉菜单命令
const handleUserCommand = async (command) => {
  switch (command) {
    case "profile":
      // 使用Tab管理方式打开个人资料页面
      openTab({
        key: "profile",
        title: "个人资料",
        path: "/profile",
        component: "ProfilePage",
        icon: "User",
      });
      break;
    case "changePassword":
      showChangePasswordDialog.value = true;
      break;
    case "logout":
      await handleLogout();
      break;
  }
};

// 处理登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm("确定要退出登录吗？", "确认退出", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    // 清除Tab状态
    clearTabsStorage();

    await userStore.logout();
    router.push("/login");
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("退出登录失败:", error);
      }
    }
  }
};

// 处理密码修改成功
const handlePasswordChanged = () => {
  ElMessage.success("密码修改成功");
  showChangePasswordDialog.value = false;
};

// 响应式处理
const handleResize = () => {
  const width = window.innerWidth;
  if (width < 768) {
    sidebarCollapsed.value = true;
  }
};

// 注释掉路由监听逻辑，避免路由变化导致的页面刷新
// 现在Tab的创建和切换完全由用户交互控制
// const routeToTabMap = {
//   '/dashboard': { key: 'home', title: '首页', component: 'HomePage', icon: 'House' },
//   '/submit': { key: 'submit', title: '提交合同', component: 'SubmitPage', icon: 'Upload' },
//   '/my-contracts': { key: 'my-contracts', title: '我的合同', component: 'MyContractsPage', icon: 'Document' },
//   '/contract-management': { key: 'contract-management', title: '合同管理', component: 'ContractManagePage', icon: 'FolderOpened' },
//   '/user-management': { key: 'user-management', title: '用户管理', component: 'UserManagePage', icon: 'UserFilled' },
//   '/system-management': { key: 'system-stats', title: '系统统计', component: 'SystemManagePage', icon: 'DataBoard' },
//   '/settings': { key: 'settings', title: '系统设置', component: 'SettingsPage', icon: 'Setting' },
//   '/profile': { key: 'profile', title: '个人资料', component: 'ProfilePage', icon: 'User' },
// };

// // 监听路由变化，自动创建对应的Tab
// watch(
//   () => route.path,
//   (newPath) => {
//     const tabConfig = routeToTabMap[newPath];
//     if (tabConfig) {
//       // 检查Tab是否已存在
//       const existingTab = tabs.value.find(tab => tab.name === tabConfig.key);
//       if (!existingTab) {
//         // 创建新Tab
//         openTab({
//           key: tabConfig.key,
//           title: tabConfig.title,
//           path: newPath,
//           component: tabConfig.component,
//           icon: tabConfig.icon,
//         });
//       } else {
//         // 激活已存在的Tab
//         setActiveTab(tabConfig.key);
//       }
//     }
//   },
//   { immediate: true }
// );

// 监听Tab状态变化，同步菜单状态
watch(
  () => activeTab.value,
  (newActiveTab) => {
    // 同步更新侧边栏菜单激活状态
    if (sidebarRef.value && sidebarRef.value.setActiveMenu) {
      sidebarRef.value.setActiveMenu(newActiveTab);
    }
  },
  { immediate: false }, // 不立即执行，避免初始化时的重复调用
);

// 组件挂载
onMounted(() => {
  // 恢复侧边栏状态 - 默认展开
  const savedCollapsed = localStorage.getItem("sidebarCollapsed");
  if (savedCollapsed !== null) {
    sidebarCollapsed.value = savedCollapsed === "true";
  } else {
    // 如果没有保存的状态，默认展开
    sidebarCollapsed.value = false;
  }

  // 响应式处理
  window.addEventListener("resize", handleResize);
  handleResize();

  // 初始化Tab状态（会自动恢复或创建默认Tab）
  initDefaultTab();

  // 初始化菜单状态
  nextTick(() => {
    if (sidebarRef.value && sidebarRef.value.setActiveMenu) {
      sidebarRef.value.setActiveMenu(activeTab.value);
    }
  });

  // 加载通知列表（会自动获取未读数量，不需要单独调用init）
  notificationStore.fetchNotifications({ page: 1, pageSize: 20 });
});

// 组件卸载
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  z-index: 1000;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
  color: #606266;
}

.sidebar-toggle:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.logo {
  font-size: 20px;
  font-weight: 700;
  color: #409eff;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-badge {
  display: flex;
  align-items: center;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  overflow: hidden;
  position: relative;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-initial {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1.2;
}

.user-role {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
}

.dropdown-icon {
  color: #c0c4cc;
  transition: transform 0.3s;
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

.layout-body {
  flex: 1;
  overflow: hidden;
}

.layout-sidebar {
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  overflow: hidden;
}

.layout-sidebar.is-collapsed {
  width: 64px !important;
}

.layout-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #f5f5f5;
  padding: 0;
}

.tab-container {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  min-height: 48px;
  display: flex;
  align-items: center;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background: #f5f5f5;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100%;
  padding: 40px;
  text-align: center;
}

.content-placeholder > div {
  max-width: 600px;
  background: white;
  padding: 60px 40px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.placeholder-icon {
  color: #c0c4cc;
  margin-bottom: 16px;
}

.placeholder-title {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin: 0 0 16px;
}

.placeholder-description {
  font-size: 16px;
  color: #606266;
  margin: 0 0 32px;
  line-height: 1.6;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  border: 1px solid;
  transition: all 0.3s ease;
}

.feature-item.completed {
  color: #67c23a;
  background: #f0f9ff;
  border-color: #c2e7b0;
}

.feature-item.pending {
  color: #e6a23c;
  background: #fdf6ec;
  border-color: #f5dab1;
}

.feature-item .el-icon {
  font-size: 16px;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-left {
    gap: 12px;
  }

  .logo {
    font-size: 18px;
  }

  .user-details {
    display: none;
  }

  .header-actions {
    gap: 12px;
  }

  .content-placeholder {
    padding: 20px;
  }

  .content-placeholder > div {
    padding: 40px 24px;
  }

  .placeholder-title {
    font-size: 24px;
  }

  .feature-list {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 16px;
  }

  .header-left {
    gap: 8px;
  }

  .logo {
    font-size: 16px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .content-placeholder > div {
    padding: 32px 20px;
  }

  .placeholder-title {
    font-size: 20px;
  }

  .placeholder-description {
    font-size: 14px;
  }
}

/* 通知下拉列表样式 */
.notification-dropdown {
  max-height: 400px;
  overflow: hidden;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.notification-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #909399;
}

.notification-empty p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item--unread {
  background-color: #f0f9ff;
}

.notification-item--unread:hover {
  background-color: #e1f5fe;
}

.notification-content {
  position: relative;
}

.notification-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.unread-dot {
  width: 6px;
  height: 6px;
  background-color: #409eff;
  border-radius: 50%;
  margin-left: 8px;
  flex-shrink: 0;
}

.notification-text {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-footer {
  padding: 8px 16px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

.notification-footer .el-button {
  font-size: 13px;
}
</style>
