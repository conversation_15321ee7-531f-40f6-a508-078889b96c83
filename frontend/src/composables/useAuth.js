/**
 * 认证状态管理 Composable
 * 使用 Vue 3 Composition API 管理用户认证状态
 */

import { ref, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { authAPI } from "@/api/auth";
import { clearContractsState } from "./useContracts";

// 全局状态
const user = ref(null);
const token = ref(localStorage.getItem("token"));
const loading = ref(false);

// 计算属性
const isAuthenticated = computed(() => !!token.value && !!user.value);
const userRole = computed(() => user.value?.role || null);
const userName = computed(() => user.value?.username || "");

// 监听 token 变化，同步到 localStorage
watch(
  token,
  (newToken) => {
    if (newToken) {
      localStorage.setItem("token", newToken);
    } else {
      localStorage.removeItem("token");
    }
  },
  { immediate: true },
);

// 监听 user 变化，同步到 localStorage
watch(
  user,
  (newUser) => {
    if (newUser) {
      localStorage.setItem("user", JSON.stringify(newUser));
    } else {
      localStorage.removeItem("user");
    }
  },
  { deep: true },
);

/**
 * 认证相关方法
 */
export function useAuth() {
  const router = useRouter();

  /**
   * 初始化认证状态
   * 从 localStorage 恢复用户信息
   */
  const initAuth = async () => {
    try {
      const savedToken = localStorage.getItem("token");
      const savedUser = localStorage.getItem("user");

      if (savedToken && savedUser) {
        token.value = savedToken;
        user.value = JSON.parse(savedUser);

        // 验证令牌是否仍然有效
        await verifyToken();
      }
    } catch (error) {
      console.warn("认证初始化失败:", error);
      clearAuth();
    }
  };

  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据
   * @param {string} credentials.username - 用户名
   * @param {string} credentials.password - 密码
   * @returns {Promise<boolean>} 登录是否成功
   */
  const login = async (credentials) => {
    try {
      loading.value = true;

      const response = await authAPI.login(credentials);

      if (response.success) {
        token.value = response.data.token;
        user.value = response.data.user;

        ElMessage.success(response.message || "登录成功");

        // 跳转到目标页面或默认页面
        const redirect =
          router.currentRoute.value.query.redirect || "/dashboard";
        await router.push(redirect);

        return true;
      }

      return false;
    } catch (error) {
      // 全局错误处理器会处理API错误，这里只需要返回失败状态
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 用户登出
   * @param {boolean} showMessage - 是否显示登出消息
   */
  const logout = async (showMessage = true) => {
    try {
      loading.value = true;

      // 调用后端登出接口
      if (token.value) {
        await authAPI.logout();
      }

      if (showMessage) {
        ElMessage.success("已安全退出");
      }
    } catch (error) {
      console.warn("登出请求失败:", error);
      // 即使后端请求失败，也要清除本地状态
    } finally {
      clearAuth();
      loading.value = false;

      // 跳转到登录页
      await router.push("/login");
    }
  };

  /**
   * 清除认证状态
   */
  const clearAuth = () => {
    token.value = null;
    user.value = null;
    localStorage.removeItem("token");
    localStorage.removeItem("user");

    // 清除合同相关的全局状态
    clearContractsState();
  };

  /**
   * 验证令牌有效性
   * @returns {Promise<boolean>} 令牌是否有效
   */
  const verifyToken = async () => {
    try {
      if (!token.value) {
        return false;
      }

      const response = await authAPI.verifyToken();

      if (response.success) {
        // 更新用户信息
        user.value = response.data.user;
        return true;
      }

      return false;
    } catch (error) {
      console.warn("令牌验证失败:", error);
      clearAuth();
      return false;
    }
  };

  /**
   * 刷新用户信息
   */
  const refreshUserInfo = async () => {
    try {
      if (!token.value) {
        return false;
      }

      const response = await authAPI.getProfile();

      if (response.success) {
        user.value = response.data;
        return true;
      }

      return false;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("刷新用户信息失败:", error);
      }
      return false;
    }
  };

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   * @returns {Promise<boolean>} 修改是否成功
   */
  const changePassword = async (passwordData) => {
    try {
      loading.value = true;

      const response = await authAPI.changePassword(passwordData);

      if (response.success) {
        ElMessage.success(response.message || "密码修改成功");
        return true;
      }

      return false;
    } catch (error) {
      if (import.meta.env.DEV) {
        console.error("修改密码失败:", error);
      }
      ElMessage.error(error.message || "修改密码失败");
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 检查用户权限
   * @param {string|Array} roles - 需要的角色
   * @returns {boolean} 是否有权限
   */
  const hasRole = (roles) => {
    if (!user.value) {
      return false;
    }

    if (typeof roles === "string") {
      return user.value.role === roles;
    }

    if (Array.isArray(roles)) {
      return roles.includes(user.value.role);
    }

    return false;
  };

  /**
   * 检查是否为管理员
   * @returns {boolean} 是否为管理员
   */
  const isAdmin = computed(() => hasRole("admin"));

  /**
   * 检查是否为审核员
   * @returns {boolean} 是否为审核员
   */
  const isReviewer = computed(() =>
    hasRole(["county_reviewer", "city_reviewer"]),
  );

  /**
   * 检查是否为员工
   * @returns {boolean} 是否为员工
   */
  const isEmployee = computed(() => hasRole("employee"));

  /**
   * 获取用户显示名称
   * @returns {string} 用户显示名称
   */
  const getDisplayName = () => {
    if (!user.value) {
      return "未登录";
    }

    return user.value.username || "用户";
  };

  /**
   * 获取用户角色显示名称
   * @returns {string} 角色显示名称
   */
  const getRoleDisplayName = () => {
    if (!user.value) {
      return "";
    }

    const roleMap = {
      admin: "管理员",
      county_reviewer: "县级审核员",
      city_reviewer: "市级审核员",
      employee: "员工",
    };

    return roleMap[user.value.role] || user.value.role;
  };

  return {
    // 状态
    user: computed(() => user.value),
    token: computed(() => token.value),
    loading: computed(() => loading.value),
    isAuthenticated,
    userRole,
    userName,
    isAdmin,
    isReviewer,
    isEmployee,

    // 方法
    initAuth,
    login,
    logout,
    clearAuth,
    verifyToken,
    refreshUserInfo,
    changePassword,
    hasRole,
    getDisplayName,
    getRoleDisplayName,
  };
}
